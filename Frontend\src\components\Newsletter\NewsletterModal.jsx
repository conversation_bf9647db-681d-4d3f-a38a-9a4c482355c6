import React, { useState } from 'react';
import { FaEnvelope, FaCheck, FaTimes, FaUser } from 'react-icons/fa';
import { toast } from 'react-toastify';

const NewsletterModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error('Email is required');
      return;
    }

    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(email.trim())) {
      toast.error('Please provide a valid email address');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/subscriber/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          name: name.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubscribed(true);
        toast.success(data.message || 'Successfully subscribed to newsletter!');
      } else {
        toast.error(data.message || 'Failed to subscribe. Please try again.');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setIsSubscribed(false);
    setEmail('');
    setName('');
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="newsletter-modal-overlay" onClick={handleClose}>
      <div className="newsletter-modal-content" onClick={(e) => e.stopPropagation()}>
        {/* Modal Header */}
        <div className="newsletter-modal-header">
          <div className="newsletter-modal-title">
            <FaEnvelope className="newsletter-modal-icon" />
            <h3>Stay Updated with IIM Education</h3>
          </div>
          <button className="newsletter-modal-close" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Modal Body */}
        <div className="newsletter-modal-body">
          {!isSubscribed ? (
            <>
              <p className="newsletter-modal-description">
                Subscribe to our newsletter and get notified about new courses, 
                announcements, and educational content!
              </p>

              <form onSubmit={handleSubmit} className="newsletter-modal-form">
                <div className="newsletter-form-group">
                  <label htmlFor="newsletter-name">
                    <FaUser className="form-icon" />
                    Name (Optional)
                  </label>
                  <input
                    type="text"
                    id="newsletter-name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter your name"
                    className="newsletter-form-input"
                  />
                </div>

                <div className="newsletter-form-group">
                  <label htmlFor="newsletter-email">
                    <FaEnvelope className="form-icon" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="newsletter-email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="newsletter-form-input"
                    required
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="newsletter-submit-btn"
                >
                  {isSubmitting ? 'Subscribing...' : 'Subscribe Now'}
                </button>
              </form>

              <p className="newsletter-privacy">
                We respect your privacy. Unsubscribe at any time.
              </p>
            </>
          ) : (
            <div className="newsletter-success">
              <div className="success-icon">
                <FaCheck />
              </div>
              <h4>Thank You!</h4>
              <p>
                You've successfully subscribed to our newsletter. 
                Check your email for a confirmation message.
              </p>
              <div className="newsletter-success-actions">
                <button 
                  className="newsletter-reset-btn"
                  onClick={handleReset}
                >
                  Subscribe Another Email
                </button>
                <button 
                  className="newsletter-close-btn"
                  onClick={handleClose}
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewsletterModal;
